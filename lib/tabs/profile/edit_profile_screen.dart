import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/tabs/profile/edit_profile_controller.dart';
import 'package:shopping/tabs/profile/widgets/profile_info_property_widget.dart';
import 'package:shopping/utils/extension/string_extension.dart';

import '../../enum/sex_enum.dart';

class EditProfileScreen extends StatelessWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditProfileController>(
      init: EditProfileController(),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: AppColors.white,
            scrolledUnderElevation: 0,
            title: const Text(
              "CHỈNH SỬA TÀI KHOẢN",
            ),
            surfaceTintColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withValues(alpha:0.4),
            titleTextStyle: AppTextStyle.s16Bold.copyWith(color: Colors.black),
            leading: IconButton(
              icon: SvgPicture.asset(
                SvgPath.svgIconBack,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 20,
                ),
                ProfileInforTextFieldWidget(
                  errorText: controller.isValidateName.value == true
                      ? controller.user?.fullname?.isEmpty == true
                          ? 'Tên không được để trống'
                          : controller.user?.fullname?.validateName() == false
                              ? null
                              : 'Tên không hợp lệ'
                      : null,
                  inputBorder: OutlineInputBorder(
                    borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  // readOnly: true,
                  title: 'Tên khách hàng',
                  maxLength: 50,
                  onChanged: (value) {
                    controller.editName(value);
                    controller.isValidateName.value = false;
                  },
                  onSubmitted: (value) {
                    controller.editName(value);
                    controller.isValidateName.value = true;
                  },
                  text: controller.user?.fullname?.trim(),
                ),
                SizedBox(
                  height: 15,
                ),
                ProfileInforTextFieldWidget(
                  errorText: controller.isValidatePhone.value == true
                      ? controller.user?.phoneNumber?.isEmpty == true
                          ? 'Số điện thoại không được để trống'
                          : controller.user?.phoneNumber?.validatePhone() == false
                              ? null
                              : 'Số điện thoại không hợp lệ'
                      : null,
                  keyBoardType: TextInputType.number,
                  title: 'Số điện thoại',
                  onChanged: (value) {
                    controller.editPhone(value);
                    controller.isValidatePhone.value = false;
                  },
                  onSubmitted: (value) {
                    controller.editPhone(value);
                    controller.isValidatePhone.value = true;
                  },
                  text: controller.user?.phoneNumber?.trim(),
                  maxLength: 20,
                ),
                SizedBox(
                  height: 15,
                ),
                ProfileInforTextFieldWidget(
                  errorText: controller.isValidateEmail.value == true
                      ? controller.user?.email?.isEmpty == true
                          ? null
                          : controller.user?.email?.validateEmail() == false
                              ? null
                              : 'Chỉ nhập định dạng Email'
                      : null,

                  // readOnly: true,
                  title: 'Email',
                  onChanged: (value) {
                    controller.editMail(value);
                    controller.isValidateEmail.value = false;
                  },
                  onSubmitted: (value) {
                    controller.editMail(value);
                    controller.isValidateEmail.value = true;
                  },
                  text: controller.user?.email,
                  maxLength: 100,
                ),
                SizedBox(
                  height: 15,
                ),

                //Chưa thống nhất kiểu dữ liệu
                ProfileInforTextFieldWidget(
                  errorText: controller.isValidateDob.value == true
                      ? controller.user?.birthday?.validateDate() == true
                          ? null
                          : 'Ngày sinh không hợp lệ'
                      : null,
                  isDate: true,
                  title: 'Ngày sinh',
                  onChanged: (value) {
                    controller.editDob(value);
                    controller.isValidateDob.value = false;
                    // print(controller.user?.birthday?.validateDate());
                  },
                  onSubmitted: (value) {
                    controller.editDob(value);
                    controller.isValidateDob.value = true;
                  },
                  text: controller.user?.birthday,
                  pickDate: (value) {
                    controller.editDob(value);
                    controller.isValidateDob.value = true;
                  },
                ),
                SizedBox(
                  height: 15,
                ),
                LableWidget(
                  title: 'Giới tính',
                  isRequired: false,
                ),
                ProfileInforPickerWidget(
                  key: ValueKey('gt'),
                  // title: 'Địa chỉ',
                  isRequire: true,
                  // hintText: 'Tỉnh/TP',
                  listData: SexEnum.values,
                  value: SexEnum.values.firstWhereOrNull((element) => element.id == controller.user?.sex),
                  pickData: (value) {
                    // controller.editGender(SexEnum.fromName(value).id);
                    controller.editGender(value);
                  },
                ),
                // SizedBox(
                //   height: 10,
                // ),
                ProfileInforTextFieldWidgetOnlyNumber(
                  maxLength: 15,
                  keyBoardType: TextInputType.number,
                  readOnly: false,
                  title: 'CCCD/CMND',
                  onChanged: (value) {
                    controller.editCCCD(value);
                  },
                  onSubmitted: (value) {
                    controller.editCCCD(value);
                  },
                  text: controller.user?.cccd,
                ),
                SizedBox(
                  height: 15,
                ),
                ProfileInforPickerWidget(
                  key: ValueKey('diachi'),
                  title: 'Địa chỉ',
                  isRequire: true,
                  hintText: 'Tỉnh/TP',
                  listData: controller.listAddress?[0],
                  value: controller.listAddress?[0]?.firstWhereOrNull((element) => element.id == controller.listIntAddress[0]),
                  pickData: (value) {
                    controller.pickAddress(index: 0, value: value);
                  },
                ),

                ProfileInforPickerWidget(
                  key: ValueKey('quan/huyen'),
                  value: controller.listAddress?[1]?.firstWhereOrNull((element) => element.id == controller.listIntAddress[1]),
                  isRequire: true,
                  hintText: 'Quận/Huyện',
                  listData: controller.listAddress?[1],
                  pickData: (value) {
                    controller.pickAddress(index: 1, value: value);
                  },
                ),
                ProfileInforPickerWidget(
                  key: ValueKey('xa/phuong'),
                  value: controller.listAddress?[2]?.firstWhereOrNull((element) => element.id == controller.listIntAddress[2]),
                  isRequire: true,
                  hintText: 'Xã/Phường',
                  listData: controller.listAddress?[2],
                  pickData: (value) {
                    controller.pickAddress(index: 2, value: value);
                  },
                ),
                ProfileInforPickerWidget(
                  key: ValueKey('pho/ap/khu'),
                  value: controller.listAddress?[3]?.firstWhereOrNull((element) => element.id == controller.listIntAddress[3]),
                  isRequire: true,
                  hintText: 'Phố/Ấp/Khu',
                  listData: controller.listAddress?[3],
                  pickData: (value) {
                    controller.pickAddress(index: 3, value: value);
                  },
                ),
                SizedBox(height: 5),
                ProfileInforTextFielNoLabledWidget(
                  hintText: 'Địa chỉ chi tiết',
                  onChanged: (value) {
                    controller.editAddress(value);
                  },
                  onSubmitted: (value) {
                    controller.editAddress(value);
                  },
                  text: controller.user?.address,
                ),
                SizedBox(height: 30),
                Align(
                  alignment: FractionalOffset.bottomCenter,
                  child: Builder(builder: (context) {
                    return GestureDetector(
                      onTap: () async {
                        await controller.saveUser(context, callBack: () {
                          Navigator.of(context)
                            ..pop()
                            ..pop();
                        });
                        controller.runValidate();
                      },
                      child: Container(
                        height: 46,
                        decoration: BoxDecoration(
                          color: AppColors.blue30AAB7,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            "LƯU",
                            style: AppTextStyle.s16Medium.copyWith(
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
                SizedBox(height: 50)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
