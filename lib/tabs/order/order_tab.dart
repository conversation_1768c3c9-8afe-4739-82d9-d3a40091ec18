import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/tabs/order/order_controller.dart';
import 'package:shopping/tabs/order/widgets/refund_order_tab.dart';
import 'package:shopping/tabs/order/widgets/tab_order_widget.dart';

import '../../constants/app_assets_paths.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_style.dart';
import 'widgets/all_order_tab.dart';
import 'widgets/fail_order_tab.dart';
import 'widgets/processing_order_tab.dart';
import 'widgets/success_order_tab.dart';

const List<String> listTitle = [
  'Tất cả',
  'Đang thực hiện',
  'Thành công',
  'Thất bại',
  'Hoàn tiền'
];
const List<Color> listColor = [
  Colors.black,
  AppColors.blue005BF9,
  AppColors.blue005BF9,
  AppColors.redCE3722
];

class OrderTab extends StatelessWidget {
  // late OrderController controller;
  @override
  Widget build(BuildContext context) {
    return orderWidget(context);
  }

  Widget orderWidget(BuildContext context) {

    return GetBuilder<OrderController>(
        init: OrderController(),
        builder: (controller) => Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                titleSpacing: 0,
                scrolledUnderElevation: 0,
                centerTitle: true,
                surfaceTintColor: AppColors.white,
                elevation: 5,
                shadowColor: AppColors.black.withValues(alpha:0.4),
                leading: Obx(() => controller.isSearch.value == true
                    ? GestureDetector(
                        child: Container(
                          width: 20,
                          height: 20,
                          color: AppColors.white,
                          child: SvgPicture.asset(
                            SvgPath.svgArrowBack,
                            color: AppColors.black,
                            fit: BoxFit.scaleDown,
                          ),
                        ),
                        onTap: () async {
                          if (controller.isSearch.value) {
                            controller.toggleSearch();
                            // await controller.getListData();
                          } else {
                            Navigator.pop(context);
                          }
                        },
                      )
                    : const SizedBox.shrink()),
                // IconButton(
                //   icon: SvgPicture.asset(SvgPath.svgIconBack),
                //   onPressed: () => Navigator.of(context).pop(),
                // ),
                title: Obx(
                  () => controller.isSearch.value == true
                      ? _SearchField(controller: controller)
                      // Container(
                      //     // color: Colors.red,
                      //     height: 40,
                      //     child: AppTextField(
                      //       labelStyle: AppTextStyle.s13Medium.copyWith(color: Colors.black),
                      //       onChanged: (value) {
                      //         controller.onSearch(value);
                      //       },
                      //       onFieldSubmitted: (value) {
                      //         controller.onSearch(value);
                      //       },
                      //     ),
                      //   )
                      : Text(
                          "ĐƠN HÀNG",
                          style: AppTextStyle.s16Bold.copyWith(
                            color: AppColors.black2E2E2E,
                          ),
                        ),
                ),
                actions: <Widget>[
                  Obx(() => controller.isSearch.value
                      ? const SizedBox(width: 24)
                      : Container(
                          width: 46,
                          height: 46,
                          margin: EdgeInsets.only(right: 15),
                          // decoration: BoxDecoration(
                          //   borderRadius: BorderRadius.circular(23),
                          //   color: AppColors.greyF1F1F3,
                          // ),
                          child: Center(
                            child: IconButton(
                              icon: SvgPicture.asset(
                                SvgPath.svgIconSearch,
                              ),
                              onPressed: () {
                                // print(AppTextStyle().fonts.assets.length);
                                controller.toggleSearch();
                              },
                            ),
                          ),
                        )),
                  // IconButton(
                  //   icon: Obx(
                  //     () => controller.isSearch.value == true
                  //         ? Icon(
                  //             Icons.close,
                  //             color: AppColors.blue005BF9,
                  //           )
                  //         : SvgPicture.asset(
                  //             SvgPath.svgIconOrderSearch,
                  //             width: 44,
                  //             height: 44,
                  //           ),
                  //   ),
                  //   onPressed: () {
                  //     controller.changeSearch(!controller.isSearch.value);
                  //   },
                  // ),
                ],
                backgroundColor: Colors.white,
              ),
              body: Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  children: [
                    SizedBox(
                      height: 30,
                      child: ListView.separated(
                        separatorBuilder: (c, i) => SizedBox(width: 10),
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (c, i) => GestureDetector(
                          onTap: () {
                            controller.changePage(i);
                          },
                          child: Obx(
                            () => TabOrderWidget(
                              bgColor: AppColors.blue30AAB7,
                              text: listTitle[i],
                              index: i,
                              isActive: controller.page_index == i,
                            ),
                          ),
                        ),
                        // separatorBuilder: (c, i) => SizedBox(
                        //   width: 10,
                        // ),
                        itemCount: listTitle.length,
                      ),
                    ),
                    SizedBox(height: 15),
                    Expanded(child: Obx(() {
                      switch (controller.page_index.value) {
                        case 0:
                          return AllOrderTab();
                        case 1:
                          return ProcessingOrderTab();
                        case 2:
                          return SuccessOrderTab();
                        case 3:
                          return FailOrderTab();
                        case 4:
                          return RefundOrderTab();
                        default:
                          return const SizedBox.shrink();
                      }
                    })),
                  ],
                ),
              ),
            ));
  }
}

class _SearchField extends StatelessWidget {
  const _SearchField({required this.controller});
  final OrderController controller;
  @override
  Widget build(BuildContext context) {
    return Container(
        height: 44,
        child: TextField(
          style: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
          maxLength: 200,
          controller: controller.searchController,
          onChanged: controller.onChangeSetValue,
          cursorColor: AppColors.black2E2E2E,
          keyboardType: TextInputType.text,
          autofocus: true,
          onEditingComplete: () {
            controller.onSearch();
          },
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            hintText: 'Nhập từ khoá tìm kiếm',
            hintStyle:
                AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
            filled: true,
            fillColor: AppColors.greyF5F6F9,
            counterText: '',
            border: const UnderlineInputBorder(),
            enabledBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(100),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(100),
            ),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: controller.onSearch,
                  child: Padding(
                    padding: const EdgeInsets.all(15),
                    child: SvgPicture.asset(
                      SvgPath.svgIconSearch,
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }
}
