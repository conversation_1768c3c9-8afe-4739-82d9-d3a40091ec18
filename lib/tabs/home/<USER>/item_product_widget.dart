import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/utils/check_overflow_text.dart';

class ItemProductWidget extends StatelessWidget {
  const ItemProductWidget(
      {super.key, required this.product, required this.openBuy});
  final Product? product;
  final Function() openBuy;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage(ImagePath.imgBgNomarlProduct),
              fit: BoxFit.fill)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(flex: 10, child: SizedBox.shrink()),
            Expanded(
              flex: 24,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  (product?.isShowIconWithPosition(1) ?? false)
                      ? SvgPicture.asset(
                          SvgPath.svgIconBox,
                          colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn),
                        )
                      : SizedBox(),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      product?.getContent(1) ?? '',
                      style: AppTextStyle.s20SemiBold.copyWith(
                        color: AppColors.black,
                      ),
                      overflow: TextOverflow.clip,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                  Visibility(
                      visible: product?.isPriceContent(1) ?? false,
                      // visible: true,
                      child: Text(
                        'đ',
                        style: AppTextStyle.s10Bold.copyWith(
                            color: AppColors.black,
                            decoration: TextDecoration.underline),
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
            Expanded(flex: 11, child: SizedBox.shrink()),
            Expanded(
                flex: 25,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Text(
                        product?.getContent(2) ?? '',
                        style: AppTextStyle.s20SemiBold
                            .copyWith(color: AppColors.black),
                        overflow: TextOverflow.clip,
                        maxLines: 1,
                        softWrap: false,
                      ),
                    ),
                    Visibility(
                        visible: product?.isPriceContent(2) ?? false,
                        // visible: true,
                        child: Text(
                          'đ',
                          style: AppTextStyle.s10Bold.copyWith(
                              color: AppColors.black,
                              decoration: TextDecoration.underline),
                          overflow: TextOverflow.ellipsis,
                        ))
                  ],
                )),
            Expanded(flex: 8, child: SizedBox.shrink()),
            Expanded(
                flex: 16,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: _ItemPackageLineDetail(
                        iconPath: (product?.isShowIconWithPosition(3) ?? false)
                            ? SvgPath.svgIconCapacity
                            : '',
                        infor: product?.getContent(3) ?? '',
                        color: AppColors.black,
                      ),
                    ),
                    Visibility(
                        visible: product?.isPriceContent(3) ?? false,
                        // visible: true,
                        child: Text(
                          'đ',
                          style: AppTextStyle.s8.copyWith(
                              color: AppColors.black,
                              decoration: TextDecoration.underline),
                          overflow: TextOverflow.ellipsis,
                        ))
                  ],
                )),
            Expanded(flex: 3, child: SizedBox.shrink()),
            // update icons
            Expanded(
                flex: 24,
                child:
                    // content 4
                    Row(
                  children: [
                    (product?.isShowIconWithPosition(4) ?? false)
                        ? SvgPicture.asset(SvgPath.svgIconCapacity)
                        : SizedBox(),
                    const SizedBox(width: 4),
                    Flexible(
                      child: LayoutBuilder(builder: (context, constraints) {
                        double maxWidth = constraints.maxWidth;
                        String content4 =
                            product?.getContent(4) ?? '';
                        if (content4.isNotEmpty &&
                            !(product?.isPriceContent(4) ?? false)) {
                          content4 = '$content4 ';
                        }
                        double textWidth = CheckOverFlow.textWidth(
                          text: "$content4 đ",
                          style: AppTextStyle.s11Regular.copyWith(
                            height: 16 / 11,
                            color: AppColors.black,
                          ),
                        );
                        return Container(
                          width: maxWidth,
                          child: CheckOverFlow.doesTextOverflow(
                                  text: "$content4 đ",
                                  style: AppTextStyle.s11Regular,
                                  maxWidth: maxWidth)
                              ? Text(
                                  content4,
                                  overflow: TextOverflow.clip,
                                  maxLines: 1,
                                  softWrap: false,
                                  style: AppTextStyle.s11Regular.copyWith(
                                    height: 16 / 11,
                                    color: AppColors.black,
                                  ),
                                )
                              : Row(
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          content4,
                                          overflow: TextOverflow.clip,
                                          maxLines: 1,
                                          softWrap: false,
                                          style:
                                              AppTextStyle.s11Regular.copyWith(
                                            height: 16 / 11,
                                            color: AppColors.black,
                                          ),
                                        ),
                                        Visibility(
                                            visible:
                                                product?.isPriceContent(4) ??
                                                    false,
                                            // visible: true,
                                            child: Text(
                                              'đ ',
                                              style: AppTextStyle.s8.copyWith(
                                                  color: AppColors.black,
                                                  decoration:
                                                      TextDecoration.underline),
                                              overflow: TextOverflow.ellipsis,
                                            )),
                                      ],
                                    ),
                                    SizedBox(width: 2),
                                    product?.getListLogo == null
                                        ? SizedBox()
                                        : ListView.builder(
                                            shrinkWrap: true,
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            scrollDirection: Axis.horizontal,
                                            itemCount:
                                                CheckOverFlow.calculateNumIcon(
                                                    maxWidth: maxWidth,
                                                    textWidth: textWidth,
                                                    listIconLength: product
                                                            ?.getListLogo
                                                            ?.length ??
                                                        0,
                                                    iconSize: 28),
                                            itemBuilder: (context, index) =>
                                                Padding(
                                              padding: EdgeInsets.only(
                                                  left: index == 0 ? 0 : 4),
                                              child: Image.network(
                                                product?.getListLogo?[index] ??
                                                    '',
                                                width: 24,
                                                height: 24,
                                              ),
                                            ),
                                          ),
                                  ],
                                ),
                        );
                      }),
                    ),
                  ],
                )
                //  product?.getListLogo == null
                //     ? SizedBox.shrink()
                //     : ListView.separated(
                //         physics: NeverScrollableScrollPhysics(),
                //         scrollDirection: Axis.horizontal,
                //         itemCount: product?.getListLogo?.length ?? 0,
                //         itemBuilder: (context, index) => Image.network(product?.getListLogo?[index] ?? ''),
                //         // SvgPicture.network(product?.getListLogo?[index] ?? ''),
                //         separatorBuilder: (_, id) => SizedBox(width: 4),
                //       ),
                ),
            Expanded(flex: 5, child: SizedBox.shrink()),
            Expanded(
                flex: 30,
                child: GestureDetector(
                  onTap: openBuy,
                  child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.blue005BF9,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Center(
                        child: Text("ĐĂNG KÝ",
                            style: AppTextStyle.s12Bold.copyWith(
                                color: AppColors.white, height: 17 / 12)),
                      )),
                )),
            Expanded(flex: 10, child: SizedBox.shrink()),
          ],
        ),
      ),
    );
  }
}

class _ItemPackageLineDetail extends StatelessWidget {
  const _ItemPackageLineDetail(
      {required this.iconPath,
      required this.infor,
      required this.color});
  final String iconPath;
  final String infor;
  final Color color;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        iconPath.trim().isNotEmpty
            ? SvgPicture.asset(
                iconPath,
                colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
              )
            : SizedBox(),
        const SizedBox(width: 4),
        Flexible(
          child: Text(infor,
              overflow: TextOverflow.clip,
              maxLines: 1,
              softWrap: false,
              style: AppTextStyle.s11Regular
                  .copyWith(height: 16 / 11, color: color)),
        )
      ],
    );
  }
}
