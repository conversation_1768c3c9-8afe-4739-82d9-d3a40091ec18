import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/tabs/home/<USER>/hot_fix_news.dart';
import 'package:shopping/tabs/home/<USER>/news_detail_view.dart';

import '../../../constants/app_assets_paths.dart';

class NewsView extends StatelessWidget {
  final List<String> listTitles = [
    'Chúc mừng KH trúng thưởng CTKM Sinh nhật nóng - Cháy mùa bóng Tuần 1 Chúc mừng KH trúng thưởng CTKM Sinh nhật nóng...',
    '<PERSON><PERSON>ch đăng ký 4G VinaPhone data cập nhật mới nhất tháng 6/2024',
    '<PERSON><PERSON> pháp mua thêm dung lượng cho sim 3G/4G/5G VinaPhone ngày: Hướng dẫn chi tiết và những điều cần biết.',
    'Mạng 4G VinaPhone yếu là do đâu? Xử lý như thế nào?',
    'Ứng phút gọi VinaPhone: Hướng dẫn và những điều cần biết',
  ];

  final List<String> listBody = [
    'Trong kỳ quay thưởng Tuần 1 ngày 15/5/2024, CTKM "Sinh nhật nóng - Cháy mùa bóng" đã tìm ra 280 chủ nhân may mắn đầu tiên sở hữu các giải thưởng giá trị như TV Samsung 75 inch, ĐT Samsung Galaxy Z Flip 5, Smartphone Hapi20, Loa Bluetooth JBL Go 3. ',
    'Từ ngày 10/05/2024, khách hàng MyTV Fix đang sử dụng gói cước Trải Nghiệm có thể nâng cấp',
    'Từ ngày 10/05/2024, khách hàng MyTV Fix đang sử dụng gói cước Trải Nghiệm có thể nâng cấp',
    'Từ ngày 10/05/2024, khách hàng MyTV Fix đang sử dụng gói cước Trải Nghiệm có thể nâng cấp',
    'Từ ngày 10/05/2024, khách hàng MyTV Fix đang sử dụng gói cước Trải Nghiệm có thể nâng cấp',
  ];
// lấy ảnh bên web
  final List<String> listIconPaths = [
    'https://muasam-stag.vivas.vn/_nuxt/tinntuc.JqLuEObQ.jpeg',
    'https://muasam-stag.vivas.vn/_nuxt/4g.CmpzpRWs.jpg',
    'https://muasam-stag.vivas.vn/_nuxt/muathem.DpPb1Zsd.jpg',
    'https://muasam-stag.vivas.vn/_nuxt/mangyeu.CjNIRqT-.jpg',
    'https://muasam-stag.vivas.vn/_nuxt/ungmang.CsLie0Rb.jpg',
  ];
  final List<String> listDate = [
    '21/05/2024',
    '23/05/2024',
    '25/05/2024',
    '26/05/2024',
    '21/05/2024',
  ];
  NewsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(SvgPath.svgIconNew),
              SizedBox(
                width: 5,
              ),
              Text("TIN TỨC NỔI BẬT",
                  style: AppTextStyle.s16Bold.copyWith(
                    color: AppColors.black,
                  )),
              Spacer(),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => NewsDetailView(
                              webLink: NewsHtmlElement.newsMyTV_1())));
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text("Xem thêm",
                        style: AppTextStyle.s12Medium.copyWith(
                          color: AppColors.black,
                        )),
                    SizedBox(
                      width: 5,
                    ),
                    SvgPicture.asset(SvgPath.svgWatchMore)
                  ],
                ),
              )
            ],
          ),
          const SizedBox(height: 19),
          ListView.builder(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: listTitles.length,
              itemBuilder: (_, index) {
                return Padding(
                    padding: const EdgeInsets.only(right: 0),
                    child: ItemNewsWidget(
                      iconPath: listIconPaths[index],
                      title: listTitles[index],
                      date: listDate[index],
                      body: listBody[index],
                      index: index,
                    ));
              })
        ],
      ),
    );
  }
}

class ItemNewsWidget extends StatelessWidget {
  const ItemNewsWidget(
      {super.key,
      required this.title,
      required this.iconPath,
      required this.date,
      required int this.index,
      required this.body});
  final String title;
  final String iconPath;
  final String date;
  final String body;
  final int index;

  @override
  Widget build(BuildContext context) {
    if (index == 0) {
      return GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => NewsDetailView(
                      webLink: NewsHtmlElement.lisHtmlNews()[index])));
        },
        child: Container(
          margin: EdgeInsets.only(bottom: 10),
          child: PhysicalModel(
            color: Colors.transparent,
            elevation: 4,
            shadowColor: Colors.black.withValues(alpha:0.4),
            borderRadius: const BorderRadius.all(Radius.circular(14.0)),
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha:0.2), blurRadius: 5)
                ],
                borderRadius: const BorderRadius.all(Radius.circular(14.0)),
                color: Colors.white,
              ),
              child: Column(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(14),
                        topRight: Radius.circular(14)),
                    child: Image.network(iconPath,
                        height: 210, fit: BoxFit.cover, width: double.infinity,
                        errorBuilder: (context, exception, stackTrack) {
                      return Container(
                        width: double.infinity,
                        height: 210,
                        child: Image.asset(
                            'packages/shopping/assets/images/img_new_3.jpg'),
                      );
                    }),
                  ),
                  Container(
                    padding: EdgeInsets.all(10),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(title,
                              style: AppTextStyle.s14SemiBold.copyWith(
                                color: Colors.black,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(body,
                              style: AppTextStyle.s13Regular.copyWith(
                                color: Colors.black,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.access_time_sharp,
                              color: AppColors.grey7F8184,
                              size: 15,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Align(
                                alignment: Alignment.centerLeft,
                                child: Text(date,
                                    style: AppTextStyle.s12Regular.copyWith(
                                      color: AppColors.grey7F8184,
                                    ))),
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => NewsDetailView(
                      webLink: NewsHtmlElement.lisHtmlNews()[index])));
        },
        child: Container(
          margin: EdgeInsets.only(bottom: 10),
          width: double.infinity,
          height: 80.0,
          child: PhysicalModel(
            color: Colors.transparent,
            elevation: 4,
            shadowColor: Colors.black.withValues(alpha:0.4),
            borderRadius: const BorderRadius.all(Radius.circular(14.0)),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha:0.2), blurRadius: 5)
                ],
                borderRadius: const BorderRadius.all(Radius.circular(14.0)),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(14.0),
                        bottomLeft: Radius.circular(14.0)),
                    child: Image.network(iconPath,
                        height: 80.0, fit: BoxFit.cover, width: 80.0,
                        errorBuilder: (context, exception, stackTrack) {
                      return Container(
                        width: 80.0,
                        height: 80.0,
                        child: Image.asset(
                            'packages/shopping/assets/images/img.png'),
                      );
                    }),
                  ),
                  Flexible(
                      child: Container(
                    padding: EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(title,
                              style: AppTextStyle.s14SemiBold.copyWith(
                                color: Colors.black,
                                fontFamily: 'montserrat',
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis),
                        ),
                        SizedBox(
                          height: 2,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.access_time_sharp,
                              color: AppColors.grey7F8184,
                              size: 15,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Align(
                                alignment: Alignment.centerLeft,
                                child: Text(date,
                                    style: AppTextStyle.s12Regular.copyWith(
                                      color: AppColors.grey7F8184,
                                    ))),
                          ],
                        )
                      ],
                    ),
                  ))
                ],
              ),
            ),
          ),
        ),
      );
    }
  }
}
